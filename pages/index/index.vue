<template>
	<view class="index-page">
		<jy-index-header :scroll-height="scrollTop"></jy-index-header>

		<jy-image-bg>
			<view class="btn1"></view>
		</jy-image-bg>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				scrollTop: 0,
				headerHeight: 0
			};
		},
		mounted() {
			// 计算header高度
			this.calculateHeaderHeight();
		},
		methods: {
			
			calculateHeaderHeight() {
				// 获取系统信息
				const systemInfo = uni.getSystemInfoSync();
				const statusBarHeight = systemInfo.statusBarHeight || 0;
				const navBarHeight = 44; // 导航栏高度，单位px
				this.headerHeight = statusBarHeight + navBarHeight;
			}
		}
	}
</script>

<style lang="scss">
.btn1{
	position: absolute;
	background: #000;
	width: 100rpx;
	height: 100rpx;
	top: 300rpx;
	left: 200rpx;
}
</style>
