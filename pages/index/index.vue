<template>
	<view class="index-page">
		<jy-index-header :scroll-height="scrollTop"></jy-index-header>

		<!-- 页面内容 -->
		<scroll-view
			class="scroll-content"
			scroll-y
			@scroll="onScroll"
			:style="{ paddingTop: headerHeight + 'px' }"
		>
			<!-- 轮播图区域 -->
			<view class="banner-section">
				<view class="banner-placeholder">
					<text class="banner-text">轮播图区域</text>
				</view>
			</view>

			<!-- 功能菜单区域 -->
			<view class="menu-section">
				<view class="menu-title">快捷功能</view>
				<view class="menu-grid">
					<view class="menu-item" v-for="item in 8" :key="item">
						<view class="menu-icon"></view>
						<text class="menu-text">功能{{item}}</text>
					</view>
				</view>
			</view>

			<!-- 内容列表区域 -->
			<view class="content-section">
				<view class="content-title">推荐内容</view>
				<view class="content-list">
					<view class="content-item" v-for="item in 20" :key="item">
						<view class="content-left">
							<view class="content-avatar"></view>
						</view>
						<view class="content-right">
							<text class="content-title-text">内容标题 {{item}}</text>
							<text class="content-desc">这是内容描述信息，用于测试滚动效果和header透明度变化</text>
							<text class="content-time">2024-01-{{item < 10 ? '0' + item : item}}</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				scrollTop: 0,
				headerHeight: 0
			};
		},
		mounted() {
			// 计算header高度
			this.calculateHeaderHeight();
		},
		methods: {
			onScroll(e) {
				this.scrollTop = e.detail.scrollTop;
			},
			calculateHeaderHeight() {
				// 获取系统信息
				const systemInfo = uni.getSystemInfoSync();
				const statusBarHeight = systemInfo.statusBarHeight || 0;
				const navBarHeight = 44; // 导航栏高度，单位px
				this.headerHeight = statusBarHeight + navBarHeight;
			}
		}
	}
</script>

<style lang="scss">
.index-page {
	height: 100vh;

	.scroll-content {
		height: 100vh;

		.banner-section {
			height: 400rpx;
			margin: 20rpx;

			.banner-placeholder {
				height: 100%;
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				border-radius: 20rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				.banner-text {
					color: white;
					font-size: 36rpx;
					font-weight: bold;
				}
			}
		}

		.menu-section {
			margin: 20rpx;
			background: white;
			border-radius: 20rpx;
			padding: 30rpx;

			.menu-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 30rpx;
			}

			.menu-grid {
				display: grid;
				grid-template-columns: repeat(4, 1fr);
				gap: 30rpx;

				.menu-item {
					display: flex;
					flex-direction: column;
					align-items: center;

					.menu-icon {
						width: 80rpx;
						height: 80rpx;
						background: #f0f0f0;
						border-radius: 20rpx;
						margin-bottom: 16rpx;
					}

					.menu-text {
						font-size: 24rpx;
						color: #666;
					}
				}
			}
		}

		.content-section {
			margin: 20rpx;
			background: white;
			border-radius: 20rpx;
			padding: 30rpx;

			.content-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 30rpx;
			}

			.content-list {
				.content-item {
					display: flex;
					padding: 20rpx 0;
					border-bottom: 1rpx solid #f0f0f0;

					&:last-child {
						border-bottom: none;
					}

					.content-left {
						margin-right: 20rpx;

						.content-avatar {
							width: 80rpx;
							height: 80rpx;
							background: #f0f0f0;
							border-radius: 50%;
						}
					}

					.content-right {
						flex: 1;

						.content-title-text {
							font-size: 28rpx;
							color: #333;
							font-weight: 500;
							display: block;
							margin-bottom: 10rpx;
						}

						.content-desc {
							font-size: 24rpx;
							color: #666;
							display: block;
							margin-bottom: 10rpx;
							line-height: 1.4;
						}

						.content-time {
							font-size: 22rpx;
							color: #999;
						}
					}
				}
			}
		}
	}
}
</style>
