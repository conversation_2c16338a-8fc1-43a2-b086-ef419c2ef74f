<template>
	<view class="text-swiper">
		<swiper 
			class="swiper-container"
			:vertical="true"
			:autoplay="autoplay"
			:interval="interval"
			:duration="duration"
			:circular="true"
			:display-multiple-items="1"
			:indicator-dots="false"
			@change="onSwiperChange"
		>
			<swiper-item 
				v-for="(text, index) in texts" 
				:key="index"
				class="swiper-item"
			>
				<text class="swiper-text" :style="textStyle">{{ text }}</text>
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
	export default {
		name: "text-swiper",
		props: {
			// 文字数组
			texts: {
				type: Array,
				default: () => []
			},
			// 是否自动播放
			autoplay: {
				type: Boolean,
				default: true
			},
			// 切换间隔时间（毫秒）
			interval: {
				type: Number,
				default: 3000
			},
			// 切换动画时长（毫秒）
			duration: {
				type: Number,
				default: 500
			},
			// 文字样式
			textStyle: {
				type: Object,
				default: () => ({
					fontSize: '26rpx',
					color: '#999'
				})
			}
		},
		data() {
			return {
				currentIndex: 0
			};
		},
		methods: {
			onSwiperChange(e) {
				this.currentIndex = e.detail.current;
				// 向父组件发送切换事件
				this.$emit('change', {
					current: this.currentIndex,
					text: this.texts[this.currentIndex]
				});
			},
			// 手动切换到指定索引
			switchTo(index) {
				if (index >= 0 && index < this.texts.length) {
					this.currentIndex = index;
				}
			},
			// 切换到下一个
			next() {
				const nextIndex = (this.currentIndex + 1) % this.texts.length;
				this.switchTo(nextIndex);
			},
			// 切换到上一个
			prev() {
				const prevIndex = this.currentIndex === 0 ? this.texts.length - 1 : this.currentIndex - 1;
				this.switchTo(prevIndex);
			}
		}
	}
</script>

<style lang="scss" scoped>
.text-swiper {
	width: 100%;
	height: 100%;
	
	.swiper-container {
		width: 100%;
		height: 100%;
		
		.swiper-item {
			display: flex;
			align-items: center;
			justify-content: flex-start;
			width: 100%;
			height: 100%;
			
			.swiper-text {
				line-height: 1.2;
				word-break: break-all;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}
}
</style>
