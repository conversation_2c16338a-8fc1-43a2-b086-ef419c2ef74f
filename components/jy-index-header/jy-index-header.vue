<template>
	<view class="jy-index-header" :style="headerStyle">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

		<!-- 导航栏内容 -->
		<view class="nav-bar">
			<!-- 左侧内容 -->
			<view class="nav-left">
				<view class="location-info" v-if="showLocationInfo">
					<u-icon name="map" size="28" color="#333"></u-icon>
					<text class="location-text">郑州</text>
				</view>
				<text class="login-text" v-else>登录</text>
			</view>

			<!-- 中间搜索框 -->
			<view class="nav-center">
				<view class="search-box">
					<u-icon name="scan" size="32" color="#999" class="scan-icon"></u-icon>
					<view class="search-text-container">
						<jy-text-swiper
							:texts="searchTexts"
							:interval="3000"
							:duration="500"
							:text-style="{ fontSize: '26rpx', color: '#999' }"
							@change="onTextChange"
						></jy-text-swiper>
					</view>
					<u-icon name="scan" size="32" color="#999" class="scan-icon"></u-icon>
				</view>
			</view>

			<!-- 右侧图标组 -->
			<view class="nav-right">
				<view class="icon-item">
					<u-icon name="bell-fill" size="40" color="#333"></u-icon>
					<view class="red-dot"></view>
				</view>
				<view class="icon-item">
					<u-icon name="chat-fill" size="40" color="#333"></u-icon>
				</view>
				<view class="icon-item">
					<u-icon name="more-circle-fill" size="40" color="#333"></u-icon>
				</view>
			</view>
		</view>

		<!-- 选项按钮区域 -->
		<view class="options-bar" v-if="showOptionsBar" :class="{ 'show': showOptionsBar }">
			<view class="option-item" v-for="(option, index) in options" :key="index" :class="{ 'active': index === 0 }">
				<text class="option-text">{{ option }}</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "jy-index-header",
		props: {
			scrollHeight: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				statusBarHeight: 0,
				searchTexts: [
					'随用随取 天天至',
					'分期付款账单无忧',
					'信用卡还款优惠',
					'理财产品收益高'
				],
				options: ['常用', '消费', '私银', '兴农通'],
				currentText: '随用随取 天天至'
			};
		},
		computed: {
			// 根据滚动高度计算背景透明度
			backgroundOpacity() {
				const threshold = 100; // 透明度变化的阈值
				if (this.scrollHeight <= 0) return 0;
				if (this.scrollHeight >= threshold) return 1;
				return this.scrollHeight / threshold;
			},
			// 动态计算header样式
			headerStyle() {
				return {
					backgroundColor: `rgba(255, 255, 255, ${this.backgroundOpacity})`,
					transition: 'background-color 0.3s ease'
				};
			},
			// 是否显示地址信息
			showLocationInfo() {
				return this.backgroundOpacity >= 0.5;
			},
			// 是否显示选项栏
			showOptionsBar() {
				return this.backgroundOpacity >= 0.5;
			}
		},
		mounted() {
			// 获取状态栏高度
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight || 0;
		},
		methods: {
			// 处理文字切换事件
			onTextChange(e) {
				this.currentText = e.text;
				// 可以在这里添加其他逻辑，比如统计或日志
				console.log('搜索文字切换到:', e.text);
			}
		}
	}
</script>

<style lang="scss">
.jy-index-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;

	.status-bar {
		width: 100%;
	}

	.nav-bar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 88rpx;
		padding: 0 20rpx;

		.nav-left {

			.login-text {
				font-size: 32rpx;
				color: #333;
				font-weight: 400;
			}

			.location-info {
				display: flex;
				align-items: center;

				.location-text {
					margin-left: 8rpx;
					font-size: 32rpx;
					color: #333;
					font-weight: 500;
				}
			}
		}

		.nav-center {
			flex: 1;
			margin: 0 20rpx;

			.search-box {
				display: flex;
				align-items: center;
				height: 60rpx;
				background: #f8f8f8;
				border-radius: 30rpx;
				padding: 0 20rpx;
				position: relative;

				.scan-icon {
					flex-shrink: 0;
				}

				.search-text-container {
					flex: 1;
					margin-left: 12rpx;
					height: 40rpx;
					overflow: hidden;
					position: relative;
				}
			}
		}

		.nav-right {
			display: flex;
			align-items: center;

			.icon-item {
				position: relative;
				margin-left: 28rpx;

				&:first-child {
					margin-left: 0;
				}

				.red-dot {
					position: absolute;
					top: -6rpx;
					right: -6rpx;
					width: 16rpx;
					height: 16rpx;
					background: #ff3b30;
					border-radius: 50%;
				}
			}
		}
	}

	// 选项栏样式
	.options-bar {
		display: flex;
		align-items: center;
		height: 88rpx;
		padding: 0 20rpx;
		background: rgba(255, 255, 255, 0.95);
		border-bottom: 1rpx solid #f0f0f0;
		opacity: 0;
		transform: translateY(-20rpx);
		transition: all 0.3s ease;

		&.show {
			opacity: 1;
			transform: translateY(0);
		}

		.option-item {
			margin-right: 60rpx;
			position: relative;

			&:last-child {
				margin-right: 0;
			}

			.option-text {
				font-size: 28rpx;
				color: #666;
				font-weight: 400;
				transition: color 0.3s ease;
			}

			&.active {
				.option-text {
					color: #333;
					font-weight: 500;
				}

				&::after {
					content: '';
					position: absolute;
					bottom: -20rpx;
					left: 50%;
					transform: translateX(-50%);
					width: 40rpx;
					height: 4rpx;
					background: #007aff;
					border-radius: 2rpx;
				}
			}
		}
	}
}
</style>