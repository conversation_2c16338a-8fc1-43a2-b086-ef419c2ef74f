<template>
	<view class="jy-index-header" :style="headerStyle">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

		<!-- 导航栏内容 -->
		<view class="nav-bar">
			<!-- 左侧登录按钮 -->
			<view class="nav-left">
				<text class="login-text">登录</text>
			</view>

			<!-- 中间搜索框 -->
			<view class="nav-center">
				<view class="search-box">
					<u-icon name="search" size="32" color="#999"></u-icon>
					<text class="search-text">分期付款账单无忧</text>
				</view>
			</view>

			<!-- 右侧图标组 -->
			<view class="nav-right">
				<view class="icon-item">
					<u-icon name="scan" size="40" color="#333"></u-icon>
				</view>
				<view class="icon-item">
					<u-icon name="bell-fill" size="40" color="#333"></u-icon>
					<view class="red-dot"></view>
				</view>
				<view class="icon-item">
					<u-icon name="chat-fill" size="40" color="#333"></u-icon>
				</view>
				<view class="icon-item">
					<u-icon name="more-circle-fill" size="40" color="#333"></u-icon>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "jy-index-header",
		props: {
			scrollHeight: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				statusBarHeight: 0
			};
		},
		computed: {
			// 根据滚动高度计算背景透明度
			backgroundOpacity() {
				const threshold = 100; // 透明度变化的阈值
				if (this.scrollHeight <= 0) return 0;
				if (this.scrollHeight >= threshold) return 1;
				return this.scrollHeight / threshold;
			},
			// 动态计算header样式
			headerStyle() {
				return {
					backgroundColor: `rgba(255, 255, 255, ${this.backgroundOpacity})`,
					transition: 'background-color 0.3s ease'
				};
			}
		},
		mounted() {
			// 获取状态栏高度
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight || 0;
		}
	}
</script>

<style lang="scss">
.jy-index-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;

	.status-bar {
		width: 100%;
	}

	.nav-bar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 88rpx;
		padding: 0 20rpx;

		.nav-left {
			.login-text {
				font-size: 32rpx;
				color: #333;
				font-weight: 400;
			}
		}

		.nav-center {
			flex: 1;
			margin: 0 24rpx;

			.search-box {
				display: flex;
				align-items: center;
				height: 60rpx;
				background: #f8f8f8;
				border-radius: 30rpx;
				padding: 0 20rpx;

				.search-text {
					margin-left: 12rpx;
					font-size: 26rpx;
					color: #999;
				}
			}
		}

		.nav-right {
			display: flex;
			align-items: center;

			.icon-item {
				position: relative;
				margin-left: 28rpx;

				&:first-child {
					margin-left: 0;
				}

				.red-dot {
					position: absolute;
					top: -6rpx;
					right: -6rpx;
					width: 16rpx;
					height: 16rpx;
					background: #ff3b30;
					border-radius: 50%;
				}
			}
		}
	}
}
</style>